import { readFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { parse as parseYaml } from 'yaml';
import { providers, getProviderConfig, getDefaultModel } from './providers.js';
const DEFAULT_CONFIG = {
    approvalMode: 'suggest',
    maxTokens: 4096,
    temperature: 0.7,
    timeout: 30000,
    enableNotifications: true,
    enableLogging: false,
    safeCommands: ['ls', 'cat', 'grep', 'find', 'head', 'tail', 'wc', 'echo', 'pwd', 'which'],
    dangerousCommands: ['rm', 'sudo', 'chmod', 'chown', 'dd', 'mkfs', 'fdisk'],
    additionalWritableRoots: []
};
let cachedConfig = null;
function autoDetectProviderAndModel() {
    const providerPriority = [
        'deepseek',
        'openai',
        'gemini',
        'mistral',
        'groq',
        'ollama',
        'azure',
        'xai',
        'arceeai',
        'openrouter'
    ];
    for (const provider of providerPriority) {
        const providerConfig = getProviderConfig(provider);
        if (providerConfig && process.env[providerConfig.envKey]) {
            return {
                provider,
                model: getDefaultModel(provider)
            };
        }
    }
    return {
        provider: 'openai',
        model: 'gpt-4'
    };
}
export function loadConfig(workingDir = process.cwd()) {
    if (cachedConfig) {
        return cachedConfig;
    }
    let config = { ...DEFAULT_CONFIG };
    const userConfig = loadUserConfig();
    if (userConfig) {
        config = { ...config, ...userConfig };
    }
    const projectConfig = loadProjectConfig(workingDir);
    if (projectConfig) {
        config = { ...config, ...projectConfig };
    }
    applyEnvironmentVariables(config);
    if (!config.provider || !config.model) {
        const autoDetected = autoDetectProviderAndModel();
        if (!config.provider) {
            config.provider = autoDetected.provider;
        }
        if (!config.model) {
            config.model = autoDetected.model;
        }
    }
    const docPath = discoverProjectDocPath(workingDir);
    if (docPath) {
        config.projectDocPath = docPath;
    }
    validateConfig(config);
    cachedConfig = config;
    return config;
}
function loadUserConfig() {
    const userConfigDir = join(homedir(), '.kritrima-ai');
    const userConfigPath = join(userConfigDir, 'config.json');
    if (!existsSync(userConfigPath)) {
        return null;
    }
    try {
        const content = readFileSync(userConfigPath, 'utf-8');
        return JSON.parse(content);
    }
    catch {
        console.warn(`Warning: Could not parse user config at ${userConfigPath}`);
        return null;
    }
}
function loadProjectConfig(workingDir) {
    const projectConfigDir = join(workingDir, '.kritrima-ai');
    const jsonPath = join(projectConfigDir, 'config.json');
    if (existsSync(jsonPath)) {
        try {
            const content = readFileSync(jsonPath, 'utf-8');
            return JSON.parse(content);
        }
        catch {
            console.warn(`Warning: Could not parse project config at ${jsonPath}`);
        }
    }
    const yamlPath = join(projectConfigDir, 'config.yaml');
    if (existsSync(yamlPath)) {
        try {
            const content = readFileSync(yamlPath, 'utf-8');
            return parseYaml(content);
        }
        catch {
            console.warn(`Warning: Could not parse project config at ${yamlPath}`);
        }
    }
    return null;
}
function applyEnvironmentVariables(config) {
    if (process.env.KRITRIMA_MODEL || process.env.KRITRIMA_AI_MODEL) {
        config.model = process.env.KRITRIMA_MODEL || process.env.KRITRIMA_AI_MODEL;
    }
    if (process.env.KRITRIMA_PROVIDER || process.env.KRITRIMA_AI_PROVIDER) {
        config.provider = (process.env.KRITRIMA_PROVIDER || process.env.KRITRIMA_AI_PROVIDER);
    }
    if (process.env.KRITRIMA_APPROVAL_MODE || process.env.KRITRIMA_AI_APPROVAL_MODE) {
        config.approvalMode = (process.env.KRITRIMA_APPROVAL_MODE || process.env.KRITRIMA_AI_APPROVAL_MODE);
    }
    if (process.env.KRITRIMA_MAX_TOKENS || process.env.KRITRIMA_AI_MAX_TOKENS) {
        const maxTokensStr = process.env.KRITRIMA_MAX_TOKENS || process.env.KRITRIMA_AI_MAX_TOKENS;
        config.maxTokens = parseInt(maxTokensStr, 10);
    }
    if (process.env.KRITRIMA_TEMPERATURE || process.env.KRITRIMA_AI_TEMPERATURE) {
        const temperatureStr = process.env.KRITRIMA_TEMPERATURE || process.env.KRITRIMA_AI_TEMPERATURE;
        config.temperature = parseFloat(temperatureStr);
    }
    if (process.env.KRITRIMA_ENABLE_NOTIFICATIONS || process.env.KRITRIMA_AI_ENABLE_NOTIFICATIONS) {
        const notificationsStr = process.env.KRITRIMA_ENABLE_NOTIFICATIONS || process.env.KRITRIMA_AI_ENABLE_NOTIFICATIONS;
        config.enableNotifications = notificationsStr === 'true';
    }
    if (process.env.KRITRIMA_ENABLE_LOGGING || process.env.KRITRIMA_AI_ENABLE_LOGGING) {
        const loggingStr = process.env.KRITRIMA_ENABLE_LOGGING || process.env.KRITRIMA_AI_ENABLE_LOGGING;
        config.enableLogging = loggingStr === 'true';
    }
}
export function discoverProjectDocPath(startDir) {
    const candidates = ['AGENTS.md', 'README.md', 'docs/README.md', 'docs/AGENTS.md'];
    let currentDir = startDir;
    while (currentDir !== dirname(currentDir)) {
        for (const candidate of candidates) {
            const fullPath = join(currentDir, candidate);
            if (existsSync(fullPath)) {
                return fullPath;
            }
        }
        currentDir = dirname(currentDir);
    }
    return null;
}
export function getConfig(workingDir) {
    return loadConfig(workingDir);
}
export function getApiKey(provider = 'openai') {
    const config = loadConfig();
    const providerInfo = getProviderConfig(provider, config.providers);
    if (providerInfo) {
        return process.env[providerInfo.envKey];
    }
    return process.env[`${provider.toUpperCase()}_API_KEY`];
}
export function getBaseUrl(provider = 'openai') {
    const config = loadConfig();
    const providerInfo = getProviderConfig(provider, config.providers);
    const envOverride = process.env[`${provider.toUpperCase()}_BASE_URL`];
    if (envOverride) {
        return envOverride;
    }
    return providerInfo?.baseURL || providers.openai.baseURL;
}
export function saveUserConfig(config) {
    const userConfigDir = join(homedir(), '.kritrima-ai');
    const userConfigPath = join(userConfigDir, 'config.json');
    if (!existsSync(userConfigDir)) {
        mkdirSync(userConfigDir, { recursive: true });
    }
    try {
        writeFileSync(userConfigPath, JSON.stringify(config, null, 2));
    }
    catch {
        console.warn(`Warning: Could not save user config to ${userConfigPath}`);
    }
}
function validateConfig(config) {
    if (!config.model || typeof config.model !== 'string') {
        throw new Error('Invalid model configuration');
    }
    if (!config.provider || typeof config.provider !== 'string') {
        throw new Error('Invalid provider configuration');
    }
    if (!['suggest', 'auto-edit', 'full-auto'].includes(config.approvalMode)) {
        throw new Error('Invalid approval mode configuration');
    }
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 200000)) {
        throw new Error('Invalid maxTokens configuration');
    }
    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
        throw new Error('Invalid temperature configuration');
    }
}
export function clearConfigCache() {
    cachedConfig = null;
}
