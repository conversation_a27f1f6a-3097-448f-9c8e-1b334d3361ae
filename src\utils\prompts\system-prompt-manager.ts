/**
 * System Prompt Manager
 * 
 * Central management system for AI agent system prompts with dynamic generation,
 * template management, context injection, and provider-specific optimization.
 */


import type {
  SystemPromptConfig,
  SystemPromptTemplate,
  SystemPromptResult,
  SystemPromptContextType,
  SystemPromptMode,

  AppConfig
} from '../../types/index.js';
import { logInfo, logError } from '../logger/log.js';
import { getSystemContext } from './context/system-context.js';
import { getProjectContext } from './context/project-context.js';
import { getSessionContext } from './context/session-context.js';
import { getToolContext } from './context/tool-context.js';
import { loadTemplate, getAvailableTemplates } from './templates/template-loader.js';
import { PromptBuilder } from './prompt-builder.js';

/**
 * Default system prompt configuration
 */
const DEFAULT_CONFIG: Partial<SystemPromptConfig> = {
  includeSystemInfo: true,
  includeProjectContext: true,
  includeToolContext: true,
  includeSessionContext: true,
  enableThinking: true,
  enablePlanning: true,
  enableValidation: false,
  safetyLevel: 'moderate',
  verbosityLevel: 'normal'
};

/**
 * System Prompt Manager Class
 */
export class SystemPromptManager {
  private templates: Map<string, SystemPromptTemplate> = new Map();
  private contextCache: Map<string, any> = new Map();
  private config: AppConfig;

  constructor(config: AppConfig) {
    this.config = config;
    this.loadTemplates();
  }

  /**
   * Generate system prompt based on configuration
   */
  async generateSystemPrompt(config: SystemPromptConfig): Promise<SystemPromptResult> {
    const startTime = Date.now();
    
    try {
      logInfo('Generating system prompt', { 
        mode: config.mode, 
        context: config.context,
        provider: config.provider 
      });

      // Merge with defaults
      const finalConfig = { ...DEFAULT_CONFIG, ...config };

      // Get appropriate template
      const template = this.getTemplate(finalConfig.mode, finalConfig.context);
      
      // Gather context data
      const contextData = await this.gatherContext(finalConfig);
      
      // Build prompt using template and context
      const builder = new PromptBuilder(template);
      const prompt = await builder
        .withContext(contextData)
        .withConfig(finalConfig)
        .withProviderOptimization(finalConfig.provider)
        .build();

      const result: SystemPromptResult = {
        prompt,
        metadata: {
          templateId: template.id,
          mode: finalConfig.mode,
          context: finalConfig.context,
          variables: contextData,
          generatedAt: Date.now(),
          tokenCount: this.estimateTokenCount(prompt)
        }
      };

      logInfo('System prompt generated successfully', {
        templateId: template.id,
        promptLength: prompt.length,
        tokenCount: result.metadata.tokenCount,
        duration: Date.now() - startTime
      });

      return result;

    } catch (error) {
      logError('Failed to generate system prompt', error instanceof Error ? error : new Error(String(error)));
      
      // Fallback to basic prompt
      return this.generateFallbackPrompt(config);
    }
  }

  /**
   * Get template for specific mode and context
   */
  private getTemplate(mode: SystemPromptMode, context: SystemPromptContextType): SystemPromptTemplate {
    // Try to find exact match
    const exactMatch = Array.from(this.templates.values()).find(
      t => t.mode === mode && t.context.includes(context)
    );
    
    if (exactMatch) {
      return exactMatch;
    }

    // Try to find mode match
    const modeMatch = Array.from(this.templates.values()).find(
      t => t.mode === mode
    );
    
    if (modeMatch) {
      return modeMatch;
    }

    // Fallback to general template
    const generalTemplate = this.templates.get('general');
    if (generalTemplate) {
      return generalTemplate;
    }

    // Create emergency fallback
    return this.createFallbackTemplate();
  }

  /**
   * Gather context data based on configuration
   */
  private async gatherContext(config: SystemPromptConfig): Promise<Record<string, any>> {
    const context: Record<string, any> = {};

    try {
      // System context
      if (config.includeSystemInfo) {
        const cacheKey = 'system_context';
        if (!this.contextCache.has(cacheKey)) {
          this.contextCache.set(cacheKey, await getSystemContext());
        }
        context.system = this.contextCache.get(cacheKey);
      }

      // Project context
      if (config.includeProjectContext) {
        const cacheKey = `project_context_${process.cwd()}`;
        if (!this.contextCache.has(cacheKey)) {
          this.contextCache.set(cacheKey, await getProjectContext(process.cwd()));
        }
        context.project = this.contextCache.get(cacheKey);
      }

      // Session context
      if (config.includeSessionContext) {
        context.session = await getSessionContext();
      }

      // Tool context
      if (config.includeToolContext) {
        const cacheKey = 'tool_context';
        if (!this.contextCache.has(cacheKey)) {
          this.contextCache.set(cacheKey, await getToolContext(this.config));
        }
        context.tools = this.contextCache.get(cacheKey);
      }

      // Custom instructions
      if (config.customInstructions) {
        context.customInstructions = config.customInstructions;
      }

      // Configuration
      context.config = {
        mode: config.mode,
        context: config.context,
        provider: config.provider,
        model: config.model,
        safetyLevel: config.safetyLevel,
        verbosityLevel: config.verbosityLevel,
        enableThinking: config.enableThinking,
        enablePlanning: config.enablePlanning,
        enableValidation: config.enableValidation
      };

      return context;

    } catch (error) {
      logError('Failed to gather context', error instanceof Error ? error : new Error(String(error)));
      return { error: 'Context gathering failed' };
    }
  }

  /**
   * Load all available templates
   */
  private loadTemplates(): void {
    try {
      const availableTemplates = getAvailableTemplates();
      
      for (const templateId of availableTemplates) {
        try {
          const template = loadTemplate(templateId);
          this.templates.set(template.id, template);
        } catch (error) {
          logError(`Failed to load template: ${templateId}`, error instanceof Error ? error : new Error(String(error)));
        }
      }

      logInfo(`Loaded ${this.templates.size} system prompt templates`);

    } catch (error) {
      logError('Failed to load templates', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Create fallback template for emergency use
   */
  private createFallbackTemplate(): SystemPromptTemplate {
    return {
      id: 'fallback',
      name: 'Emergency Fallback',
      description: 'Basic system prompt for emergency use',
      mode: 'general',
      context: ['cli', 'interactive', 'agent-loop'],
      template: `You are Kritrima AI, an advanced AI assistant with access to various tools and capabilities.

# Core Instructions
- Your main goal is to help users with their tasks and queries.
- You are designed to be helpful, accurate, and efficient.
- You have access to a wide range of tools and capabilities and can chain multiple tools together and combine them together to accomplish complex tasks automatically according to user instructions and approval policies.
- You should always follow safety guidelines and best practices.
- You should provide clear explanations and step-by-step guidance.
- You should be concise and avoid unnecessary details.
- You should be adaptive and adjust your approach based on user expertise and context.
- You should always think before you act.
- You should always validate your actions and provide feedback to the user.
- You should always ask for confirmation before making any changes to the system or user files according to the approval policy.
- You should always provide a summary of your actions and the results to the user.
- You should always thnink and create a detailed todo list for complex tasks and provide a plan before executing any actions.
- You should always check if the task is completed and if not, you should provide a reason why and ask for further instructions.
- You should always provide a way for the user to check the status of the task and the progress.
- Be helpful, accurate, and efficient 
- Use available tools when appropriate
- Follow safety guidelines and best practices
- Provide clear explanations and step-by-step guidance

# Current Context
- Mode: {{config.mode}}
- Context: {{config.context}}
- Provider: {{config.provider}}
- Model: {{config.model}}

Execute tasks thoughtfully and systematically.`,
      variables: {},
      metadata: {
        version: '1.0.0',
        author: 'System',
        created: Date.now(),
        updated: Date.now(),
        tags: ['fallback', 'emergency']
      }
    };
  }

  /**
   * Generate fallback prompt when main generation fails
   */
  private generateFallbackPrompt(config: SystemPromptConfig): SystemPromptResult {
    const fallbackTemplate = this.createFallbackTemplate();
    const basicContext = {
      config: {
        mode: config.mode,
        context: config.context,
        provider: config.provider,
        model: config.model
      }
    };

    const prompt = fallbackTemplate.template
      .replace(/\{\{config\.mode\}\}/g, config.mode)
      .replace(/\{\{config\.context\}\}/g, config.context)
      .replace(/\{\{config\.provider\}\}/g, config.provider)
      .replace(/\{\{config\.model\}\}/g, config.model);

    return {
      prompt,
      metadata: {
        templateId: 'fallback',
        mode: config.mode,
        context: config.context,
        variables: basicContext,
        generatedAt: Date.now(),
        tokenCount: this.estimateTokenCount(prompt)
      }
    };
  }

  /**
   * Estimate token count for prompt
   */
  private estimateTokenCount(text: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  /**
   * Clear context cache
   */
  clearCache(): void {
    this.contextCache.clear();
    logInfo('System prompt context cache cleared');
  }

  /**
   * Get available templates
   */
  getAvailableTemplates(): SystemPromptTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Update configuration
   */
  updateConfig(config: AppConfig): void {
    this.config = config;
    this.clearCache(); // Clear cache when config changes
  }
}

/**
 * Global system prompt manager instance
 */
let globalManager: SystemPromptManager | null = null;

/**
 * Get or create global system prompt manager
 */
export function getSystemPromptManager(config: AppConfig): SystemPromptManager {
  if (!globalManager) {
    globalManager = new SystemPromptManager(config);
  } else {
    globalManager.updateConfig(config);
  }
  return globalManager;
}

/**
 * Quick helper to generate system prompt
 */
export async function generateSystemPrompt(
  mode: SystemPromptMode,
  context: SystemPromptContextType,
  config: AppConfig,
  options: Partial<SystemPromptConfig> = {}
): Promise<SystemPromptResult> {
  const manager = getSystemPromptManager(config);
  
  const promptConfig: SystemPromptConfig = {
    mode,
    context,
    provider: config.provider,
    model: config.model,
    ...options
  };

  return manager.generateSystemPrompt(promptConfig);
}
