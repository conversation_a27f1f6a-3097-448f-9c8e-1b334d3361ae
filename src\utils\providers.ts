/**
 * Provider Configuration System
 * 
 * Defines supported AI providers with their configurations
 * Supports built-in providers and custom provider definitions
 */

import type { ProviderConfig, ProviderName } from '../types/index.js';

/**
 * Built-in AI providers with their default configurations
 */
export const providers: Record<ProviderName, ProviderConfig> = {
  openai: {
    name: "OpenAI",
    baseURL: "https://api.openai.com/v1",
    envKey: "OPENAI_API_KEY"
  },
  azure: {
    name: "Azure OpenAI",
    baseURL: "https://your-resource.openai.azure.com",
    envKey: "AZURE_OPENAI_API_KEY"
  },
  gemini: {
    name: "Google Gemini",
    baseURL: "https://generativelanguage.googleapis.com/v1beta",
    envKey: "GEMINI_API_KEY"
  },
  ollama: {
    name: "Ollama",
    baseURL: "http://localhost:11434/v1",
    envKey: "OLLAMA_API_KEY"
  },
  mistral: {
    name: "Mistral AI",
    baseURL: "https://api.mistral.ai/v1",
    envKey: "MISTRAL_API_KEY"
  },
  deepseek: {
    name: "DeepSeek",
    baseURL: "https://api.deepseek.com/v1",
    envKey: "DEEPSEEK_API_KEY"
  },
  xai: {
    name: "xAI",
    baseURL: "https://api.x.ai/v1",
    envKey: "XAI_API_KEY"
  },
  groq: {
    name: "Groq",
    baseURL: "https://api.groq.com/openai/v1",
    envKey: "GROQ_API_KEY"
  },
  arceeai: {
    name: "ArceeAI",
    baseURL: "https://api.arcee.ai/v1",
    envKey: "ARCEEAI_API_KEY"
  },
  openrouter: {
    name: "OpenRouter",
    baseURL: "https://openrouter.ai/api/v1",
    envKey: "OPENROUTER_API_KEY"
  }
};

/**
 * Get provider configuration by name
 */
export function getProviderConfig(
  providerName: string,
  customProviders?: Record<string, ProviderConfig>
): ProviderConfig | null {
  // Check custom providers first
  if (customProviders && customProviders[providerName.toLowerCase()]) {
    return customProviders[providerName.toLowerCase()];
  }
  
  // Check built-in providers
  const provider = providers[providerName.toLowerCase() as ProviderName];
  if (provider) {
    return provider;
  }
  
  return null;
}

/**
 * Get all available provider names
 */
export function getAvailableProviders(
  customProviders?: Record<string, ProviderConfig>
): string[] {
  const builtInProviders = Object.keys(providers);
  const customProviderNames = customProviders ? Object.keys(customProviders) : [];
  
  return [...builtInProviders, ...customProviderNames];
}

/**
 * Validate provider configuration
 */
export function validateProviderConfig(config: ProviderConfig): boolean {
  return !!(
    config.name &&
    config.baseURL &&
    config.envKey &&
    typeof config.name === 'string' &&
    typeof config.baseURL === 'string' &&
    typeof config.envKey === 'string'
  );
}

/**
 * Get provider display name
 */
export function getProviderDisplayName(
  providerName: string,
  customProviders?: Record<string, ProviderConfig>
): string {
  const config = getProviderConfig(providerName, customProviders);
  return config?.name || providerName;
}

/**
 * Check if provider supports a specific feature
 */
export function providerSupportsFeature(
  providerName: string,
  feature: 'images' | 'tools' | 'streaming'
): boolean {
  // Feature support matrix
  const featureMatrix: Record<string, Record<string, boolean>> = {
    openai: { images: true, tools: true, streaming: true },
    azure: { images: true, tools: true, streaming: true },
    gemini: { images: true, tools: true, streaming: true },
    ollama: { images: false, tools: true, streaming: true },
    mistral: { images: false, tools: true, streaming: true },
    deepseek: { images: false, tools: true, streaming: true },
    xai: { images: false, tools: true, streaming: true },
    groq: { images: false, tools: true, streaming: true },
    arceeai: { images: false, tools: true, streaming: true },
    openrouter: { images: true, tools: true, streaming: true }
  };
  
  const providerFeatures = featureMatrix[providerName.toLowerCase()];
  return providerFeatures?.[feature] || false;
}

/**
 * Get default model for provider
 */
export function getDefaultModel(providerName: string): string {
  const defaultModels: Record<string, string> = {
    openai: 'gpt-4',
    azure: 'gpt-4',
    gemini: 'gemini-pro',
    ollama: 'llama2',
    mistral: 'mistral-large-latest',
    deepseek: 'deepseek-chat',
    xai: 'grok-beta',
    groq: 'llama3-8b-8192',
    arceeai: 'arcee-nova',
    openrouter: 'openai/gpt-4'
  };

  return defaultModels[providerName.toLowerCase()] || 'gpt-4';
}

/**
 * Get providers that have API keys configured
 */
export function getProvidersWithApiKeys(
  customProviders?: Record<string, ProviderConfig>
): string[] {
  const allProviders = getAvailableProviders(customProviders);

  return allProviders.filter(providerName => {
    const config = getProviderConfig(providerName, customProviders);
    return config && process.env[config.envKey];
  });
}

/**
 * Get the first available provider with an API key
 */
export function getFirstAvailableProvider(
  customProviders?: Record<string, ProviderConfig>
): string | null {
  const providersWithKeys = getProvidersWithApiKeys(customProviders);
  return providersWithKeys.length > 0 ? providersWithKeys[0] : null;
}
