import { logInfo, logError } from '../logger/log.js';
import { getSystemContext } from './context/system-context.js';
import { getProjectContext } from './context/project-context.js';
import { getSessionContext } from './context/session-context.js';
import { getToolContext } from './context/tool-context.js';
import { loadTemplate, getAvailableTemplates } from './templates/template-loader.js';
import { PromptBuilder } from './prompt-builder.js';
const DEFAULT_CONFIG = {
    includeSystemInfo: true,
    includeProjectContext: true,
    includeToolContext: true,
    includeSessionContext: true,
    enableThinking: true,
    enablePlanning: true,
    enableValidation: false,
    safetyLevel: 'moderate',
    verbosityLevel: 'normal'
};
export class SystemPromptManager {
    templates = new Map();
    contextCache = new Map();
    config;
    constructor(config) {
        this.config = config;
        this.loadTemplates();
    }
    async generateSystemPrompt(config) {
        const startTime = Date.now();
        try {
            logInfo('Generating system prompt', {
                mode: config.mode,
                context: config.context,
                provider: config.provider
            });
            const finalConfig = { ...DEFAULT_CONFIG, ...config };
            const template = this.getTemplate(finalConfig.mode, finalConfig.context);
            const contextData = await this.gatherContext(finalConfig);
            const builder = new PromptBuilder(template);
            const prompt = await builder
                .withContext(contextData)
                .withConfig(finalConfig)
                .withProviderOptimization(finalConfig.provider)
                .build();
            const result = {
                prompt,
                metadata: {
                    templateId: template.id,
                    mode: finalConfig.mode,
                    context: finalConfig.context,
                    variables: contextData,
                    generatedAt: Date.now(),
                    tokenCount: this.estimateTokenCount(prompt)
                }
            };
            logInfo('System prompt generated successfully', {
                templateId: template.id,
                promptLength: prompt.length,
                tokenCount: result.metadata.tokenCount,
                duration: Date.now() - startTime
            });
            return result;
        }
        catch (error) {
            logError('Failed to generate system prompt', error instanceof Error ? error : new Error(String(error)));
            return this.generateFallbackPrompt(config);
        }
    }
    getTemplate(mode, context) {
        const exactMatch = Array.from(this.templates.values()).find(t => t.mode === mode && t.context.includes(context));
        if (exactMatch) {
            return exactMatch;
        }
        const modeMatch = Array.from(this.templates.values()).find(t => t.mode === mode);
        if (modeMatch) {
            return modeMatch;
        }
        const generalTemplate = this.templates.get('general');
        if (generalTemplate) {
            return generalTemplate;
        }
        return this.createFallbackTemplate();
    }
    async gatherContext(config) {
        const context = {};
        try {
            if (config.includeSystemInfo) {
                const cacheKey = 'system_context';
                if (!this.contextCache.has(cacheKey)) {
                    this.contextCache.set(cacheKey, await getSystemContext());
                }
                context.system = this.contextCache.get(cacheKey);
            }
            if (config.includeProjectContext) {
                const cacheKey = `project_context_${process.cwd()}`;
                if (!this.contextCache.has(cacheKey)) {
                    this.contextCache.set(cacheKey, await getProjectContext(process.cwd()));
                }
                context.project = this.contextCache.get(cacheKey);
            }
            if (config.includeSessionContext) {
                context.session = await getSessionContext();
            }
            if (config.includeToolContext) {
                const cacheKey = 'tool_context';
                if (!this.contextCache.has(cacheKey)) {
                    this.contextCache.set(cacheKey, await getToolContext(this.config));
                }
                context.tools = this.contextCache.get(cacheKey);
            }
            if (config.customInstructions) {
                context.customInstructions = config.customInstructions;
            }
            context.config = {
                mode: config.mode,
                context: config.context,
                provider: config.provider,
                model: config.model,
                safetyLevel: config.safetyLevel,
                verbosityLevel: config.verbosityLevel,
                enableThinking: config.enableThinking,
                enablePlanning: config.enablePlanning,
                enableValidation: config.enableValidation
            };
            return context;
        }
        catch (error) {
            logError('Failed to gather context', error instanceof Error ? error : new Error(String(error)));
            return { error: 'Context gathering failed' };
        }
    }
    loadTemplates() {
        try {
            const availableTemplates = getAvailableTemplates();
            for (const templateId of availableTemplates) {
                try {
                    const template = loadTemplate(templateId);
                    this.templates.set(template.id, template);
                }
                catch (error) {
                    logError(`Failed to load template: ${templateId}`, error instanceof Error ? error : new Error(String(error)));
                }
            }
            logInfo(`Loaded ${this.templates.size} system prompt templates`);
        }
        catch (error) {
            logError('Failed to load templates', error instanceof Error ? error : new Error(String(error)));
        }
    }
    createFallbackTemplate() {
        return {
            id: 'fallback',
            name: 'Emergency Fallback',
            description: 'Basic system prompt for emergency use',
            mode: 'general',
            context: ['cli', 'interactive', 'agent-loop'],
            template: `You are Kritrima AI, an advanced AI assistant with access to various tools and capabilities.

# Core Instructions
- Your main goal is to help users with their tasks and queries.
- You are designed to be helpful, accurate, and efficient.
- You have access to a wide range of tools and capabilities and can chain multiple tools together and combine them together to accomplish complex tasks automatically according to user instructions and approval policies.
- You should always follow safety guidelines and best practices.
- You should provide clear explanations and step-by-step guidance.
- You should be concise and avoid unnecessary details.
- You should be adaptive and adjust your approach based on user expertise and context.
- You should always think before you act.
- You should always validate your actions and provide feedback to the user.
- You should always ask for confirmation before making any changes to the system or user files according to the approval policy.
- You should always provide a summary of your actions and the results to the user.
- You should always thnink and create a detailed todo list for complex tasks and provide a plan before executing any actions.
- You should always check if the task is completed and if not, you should provide a reason why and ask for further instructions.
- You should always provide a way for the user to check the status of the task and the progress.
- Be helpful, accurate, and efficient 
- Use available tools when appropriate
- Follow safety guidelines and best practices
- Provide clear explanations and step-by-step guidance

# Current Context
- Mode: {{config.mode}}
- Context: {{config.context}}
- Provider: {{config.provider}}
- Model: {{config.model}}

Execute tasks thoughtfully and systematically.`,
            variables: {},
            metadata: {
                version: '1.0.0',
                author: 'System',
                created: Date.now(),
                updated: Date.now(),
                tags: ['fallback', 'emergency']
            }
        };
    }
    generateFallbackPrompt(config) {
        const fallbackTemplate = this.createFallbackTemplate();
        const basicContext = {
            config: {
                mode: config.mode,
                context: config.context,
                provider: config.provider,
                model: config.model
            }
        };
        const prompt = fallbackTemplate.template
            .replace(/\{\{config\.mode\}\}/g, config.mode)
            .replace(/\{\{config\.context\}\}/g, config.context)
            .replace(/\{\{config\.provider\}\}/g, config.provider)
            .replace(/\{\{config\.model\}\}/g, config.model);
        return {
            prompt,
            metadata: {
                templateId: 'fallback',
                mode: config.mode,
                context: config.context,
                variables: basicContext,
                generatedAt: Date.now(),
                tokenCount: this.estimateTokenCount(prompt)
            }
        };
    }
    estimateTokenCount(text) {
        return Math.ceil(text.length / 4);
    }
    clearCache() {
        this.contextCache.clear();
        logInfo('System prompt context cache cleared');
    }
    getAvailableTemplates() {
        return Array.from(this.templates.values());
    }
    updateConfig(config) {
        this.config = config;
        this.clearCache();
    }
}
let globalManager = null;
export function getSystemPromptManager(config) {
    if (!globalManager) {
        globalManager = new SystemPromptManager(config);
    }
    else {
        globalManager.updateConfig(config);
    }
    return globalManager;
}
export async function generateSystemPrompt(mode, context, config, options = {}) {
    const manager = getSystemPromptManager(config);
    const promptConfig = {
        mode,
        context,
        provider: config.provider,
        model: config.model,
        ...options
    };
    return manager.generateSystemPrompt(promptConfig);
}
